#!/usr/bin/env python3
"""
Test script to verify the enhanced prompts are working correctly.
This script tests the new enhanced prompts for candidate and position analysis.
"""

import os
import sys

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_prompts():
    """Test the enhanced prompts functionality"""
    
    print("Testing Enhanced Prompts for SmartHR")
    print("=" * 50)
    
    try:
        # Test candidate analysis prompts
        from templates.candidates_templates.candidate_analysis import (
            get_candidate_analysis_prompt,
            get_enhanced_candidate_analysis_prompt,
            get_candidate_analysis_prompt_smart,
            get_enhanced_batch_candidate_analysis_prompt
        )
        
        print("✓ Successfully imported candidate analysis functions")
        
        # Test position analysis prompts
        from templates.positions_templates.position_analysis import (
            get_position_analysis_prompt,
            get_enhanced_position_analysis_prompt,
            get_position_analysis_prompt_smart
        )
        
        print("✓ Successfully imported position analysis functions")
        
        # Test configuration
        from config.config import PROMPT_CONFIG
        print(f"✓ Configuration loaded: {PROMPT_CONFIG}")
        
        # Test prompt generation
        print("\n" + "=" * 50)
        print("Testing Prompt Generation")
        print("=" * 50)
        
        # Test original candidate prompt
        original_candidate_prompt = get_candidate_analysis_prompt()
        print(f"✓ Original candidate prompt length: {len(original_candidate_prompt)} characters")
        
        # Test enhanced candidate prompt
        enhanced_candidate_prompt = get_enhanced_candidate_analysis_prompt()
        print(f"✓ Enhanced candidate prompt length: {len(enhanced_candidate_prompt)} characters")
        
        # Test smart candidate prompt
        smart_candidate_prompt = get_candidate_analysis_prompt_smart()
        print(f"✓ Smart candidate prompt length: {len(smart_candidate_prompt)} characters")
        
        # Test original position prompt
        original_position_prompt = get_position_analysis_prompt()
        print(f"✓ Original position prompt length: {len(original_position_prompt)} characters")
        
        # Test enhanced position prompt
        enhanced_position_prompt = get_enhanced_position_analysis_prompt()
        print(f"✓ Enhanced position prompt length: {len(enhanced_position_prompt)} characters")
        
        # Test smart position prompt
        smart_position_prompt = get_position_analysis_prompt_smart()
        print(f"✓ Smart position prompt length: {len(smart_position_prompt)} characters")
        
        # Test enhanced batch prompt
        enhanced_batch_prompt = get_enhanced_batch_candidate_analysis_prompt()
        print(f"✓ Enhanced batch prompt length: {len(enhanced_batch_prompt)} characters")
        
        # Verify enhanced prompts are more detailed
        print("\n" + "=" * 50)
        print("Verification Results")
        print("=" * 50)
        
        if len(enhanced_candidate_prompt) > len(original_candidate_prompt):
            print("✓ Enhanced candidate prompt is more detailed than original")
        else:
            print("⚠ Enhanced candidate prompt may not be more detailed")
            
        if len(enhanced_position_prompt) > len(original_position_prompt):
            print("✓ Enhanced position prompt is more detailed than original")
        else:
            print("⚠ Enhanced position prompt may not be more detailed")
        
        # Check if smart functions return enhanced prompts by default
        if smart_candidate_prompt == enhanced_candidate_prompt:
            print("✓ Smart candidate function returns enhanced prompt (default configuration)")
        elif smart_candidate_prompt == original_candidate_prompt:
            print("✓ Smart candidate function returns original prompt (configuration disabled)")
        else:
            print("⚠ Smart candidate function behavior unexpected")
            
        if smart_position_prompt == enhanced_position_prompt:
            print("✓ Smart position function returns enhanced prompt (default configuration)")
        elif smart_position_prompt == original_position_prompt:
            print("✓ Smart position function returns original prompt (configuration disabled)")
        else:
            print("⚠ Smart position function behavior unexpected")
        
        print("\n" + "=" * 50)
        print("Sample Enhanced Candidate Prompt Preview")
        print("=" * 50)
        print(enhanced_candidate_prompt[:500] + "...")
        
        print("\n" + "=" * 50)
        print("Sample Enhanced Position Prompt Preview")
        print("=" * 50)
        print(enhanced_position_prompt[:500] + "...")
        
        print("\n" + "=" * 50)
        print("All tests completed successfully! ✓")
        print("Enhanced prompts are ready for use.")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_enhanced_prompts()
    sys.exit(0 if success else 1)
