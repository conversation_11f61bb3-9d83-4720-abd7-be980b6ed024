"""
Service for managing ideal candidate embeddings and vector operations.
"""

import logging
from typing import List, Dict, Any, Optional
import psycopg2

from core.config import settings
from utils.ideal_candidate_text_utils import prepare_ideal_candidate_for_embedding
from utils.utils_embeddings import generate_openai_embedding, format_vector
from controllers.ideal_candidate_controller import (
    get_ideal_candidate_by_id,
    search_similar_ideal_candidates
)

logger = logging.getLogger(__name__)


class IdealCandidateEmbeddingService:
    """Service for managing ideal candidate embeddings and vector operations."""
    
    def __init__(self):
        pass
    
    def generate_embedding_for_ideal_candidate(self, ideal_candidate_info: Dict[str, Any]) -> Optional[List[float]]:
        """
        Generate embedding for ideal candidate information.
        
        Args:
            ideal_candidate_info: The ideal candidate data dictionary
            
        Returns:
            The embedding vector or None if generation failed
        """
        try:
            # Prepare text for embedding
            embed_text = prepare_ideal_candidate_for_embedding(ideal_candidate_info)
            
            if not embed_text or embed_text.strip() == "":
                logger.warning("Empty text generated for ideal candidate embedding")
                return None
            
            # Generate embedding using existing utilities
            embedding = generate_openai_embedding(embed_text)
            
            if embedding is None:
                logger.error("Failed to generate embedding for ideal candidate")
                return None
            
            logger.info(f"Successfully generated embedding with {len(embedding)} dimensions")
            return embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding for ideal candidate: {e}")
            return None
    
    def update_ideal_candidate_embedding(self, ideal_candidate_id: str) -> bool:
        """
        Update the embedding for an existing ideal candidate.
        
        Args:
            ideal_candidate_id: The ID of the ideal candidate to update
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Get the ideal candidate
            ideal_candidate = get_ideal_candidate_by_id(ideal_candidate_id)
            if not ideal_candidate:
                logger.error(f"Ideal candidate {ideal_candidate_id} not found")
                return False
            
            # Generate new embedding
            embedding = self.generate_embedding_for_ideal_candidate(ideal_candidate.ideal_candidate_info)
            if not embedding:
                logger.error(f"Failed to generate embedding for ideal candidate {ideal_candidate_id}")
                return False
            
            # Update in database
            conn = psycopg2.connect(settings.DATABASE_URL)
            cur = conn.cursor()
            
            # Prepare text for embedding
            embed_text = prepare_ideal_candidate_for_embedding(ideal_candidate.ideal_candidate_info)
            embedding_vector = format_vector(embedding)
            
            cur.execute(
                """
                UPDATE ideal_candidates_smarthr
                SET to_be_embedded = %s, embedding = %s, embedding_generated = true, updated_at = NOW()
                WHERE id = %s
                """,
                (embed_text, embedding_vector, ideal_candidate_id)
            )
            
            rows_affected = cur.rowcount
            conn.commit()
            cur.close()
            conn.close()
            
            if rows_affected > 0:
                logger.info(f"Successfully updated embedding for ideal candidate {ideal_candidate_id}")
                return True
            else:
                logger.warning(f"No rows updated for ideal candidate {ideal_candidate_id}")
                return False
            
        except Exception as e:
            logger.error(f"Error updating embedding for ideal candidate {ideal_candidate_id}: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return False
    
    def batch_update_embeddings(self, ideal_candidate_ids: List[str]) -> Dict[str, bool]:
        """
        Update embeddings for multiple ideal candidates in batch.
        
        Args:
            ideal_candidate_ids: List of ideal candidate IDs to update
            
        Returns:
            Dictionary mapping ideal candidate ID to success status
        """
        results = {}
        
        for ideal_candidate_id in ideal_candidate_ids:
            try:
                success = self.update_ideal_candidate_embedding(ideal_candidate_id)
                results[ideal_candidate_id] = success
                
                if success:
                    logger.info(f"Successfully updated embedding for {ideal_candidate_id}")
                else:
                    logger.warning(f"Failed to update embedding for {ideal_candidate_id}")
                    
            except Exception as e:
                logger.error(f"Error updating embedding for {ideal_candidate_id}: {e}")
                results[ideal_candidate_id] = False
        
        successful_updates = sum(1 for success in results.values() if success)
        logger.info(f"Batch embedding update completed: {successful_updates}/{len(ideal_candidate_ids)} successful")
        
        return results
    
    def find_similar_ideal_candidates(
        self, 
        reference_ideal_candidate_id: str, 
        limit: int = 5,
        proj_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Find ideal candidates similar to a reference ideal candidate.
        
        Args:
            reference_ideal_candidate_id: The ID of the reference ideal candidate
            limit: Maximum number of similar candidates to return
            proj_id: Optional project ID to filter by
            
        Returns:
            List of similar ideal candidates with similarity scores
        """
        try:
            # Get the reference ideal candidate
            reference_candidate = get_ideal_candidate_by_id(reference_ideal_candidate_id)
            if not reference_candidate:
                logger.error(f"Reference ideal candidate {reference_ideal_candidate_id} not found")
                return []
            
            # Generate embedding for the reference candidate
            reference_embedding = self.generate_embedding_for_ideal_candidate(
                reference_candidate.ideal_candidate_info
            )
            
            if not reference_embedding:
                logger.error(f"Failed to generate embedding for reference candidate {reference_ideal_candidate_id}")
                return []
            
            # Search for similar candidates
            similar_candidates = search_similar_ideal_candidates(
                embedding_vector=reference_embedding,
                limit=limit + 1,  # +1 to account for the reference candidate itself
                proj_id=proj_id
            )
            
            # Filter out the reference candidate itself
            filtered_candidates = [
                candidate for candidate in similar_candidates 
                if candidate["id"] != reference_ideal_candidate_id
            ]
            
            # Limit to requested number
            return filtered_candidates[:limit]
            
        except Exception as e:
            logger.error(f"Error finding similar ideal candidates: {e}")
            return []
    
    def find_ideal_candidates_for_text(
        self, 
        text: str, 
        limit: int = 5,
        proj_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Find ideal candidates similar to a given text query.
        
        Args:
            text: The text to search for similar ideal candidates
            limit: Maximum number of candidates to return
            proj_id: Optional project ID to filter by
            
        Returns:
            List of similar ideal candidates with similarity scores
        """
        try:
            # Generate embedding for the text
            text_embedding = generate_openai_embedding(text)
            
            if not text_embedding:
                logger.error("Failed to generate embedding for search text")
                return []
            
            # Search for similar candidates
            similar_candidates = search_similar_ideal_candidates(
                embedding_vector=text_embedding,
                limit=limit,
                proj_id=proj_id
            )
            
            logger.info(f"Found {len(similar_candidates)} ideal candidates similar to provided text")
            return similar_candidates
            
        except Exception as e:
            logger.error(f"Error finding ideal candidates for text: {e}")
            return []
    
    def get_embedding_statistics(self, proj_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get statistics about ideal candidate embeddings.
        
        Args:
            proj_id: Optional project ID to filter by
            
        Returns:
            Dictionary containing embedding statistics
        """
        try:
            conn = psycopg2.connect(settings.DATABASE_URL)
            cur = conn.cursor()
            
            # Base query conditions
            where_conditions = ["is_active = true"]
            params = []
            
            if proj_id:
                where_conditions.append("proj_id = %s")
                params.append(proj_id)
            
            where_clause = " AND ".join(where_conditions)
            
            # Get total count
            cur.execute(f"SELECT COUNT(*) FROM ideal_candidates_smarthr WHERE {where_clause}", params)
            total_count = cur.fetchone()[0]
            
            # Get count with embeddings
            where_conditions.append("embedding_generated = true")
            where_clause = " AND ".join(where_conditions)
            cur.execute(f"SELECT COUNT(*) FROM ideal_candidates_smarthr WHERE {where_clause}", params)
            with_embeddings_count = cur.fetchone()[0]
            
            # Get count without embeddings
            without_embeddings_count = total_count - with_embeddings_count
            
            cur.close()
            conn.close()
            
            statistics = {
                "total_ideal_candidates": total_count,
                "with_embeddings": with_embeddings_count,
                "without_embeddings": without_embeddings_count,
                "embedding_coverage_percentage": (with_embeddings_count / total_count * 100) if total_count > 0 else 0,
                "proj_id": proj_id
            }
            
            logger.info(f"Embedding statistics: {statistics}")
            return statistics
            
        except Exception as e:
            logger.error(f"Error getting embedding statistics: {e}")
            if 'conn' in locals():
                conn.close()
            return {
                "total_ideal_candidates": 0,
                "with_embeddings": 0,
                "without_embeddings": 0,
                "embedding_coverage_percentage": 0,
                "proj_id": proj_id,
                "error": str(e)
            }


# Global instance
ideal_candidate_embedding_service = IdealCandidateEmbeddingService()
