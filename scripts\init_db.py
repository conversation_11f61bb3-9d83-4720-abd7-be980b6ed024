import os

import psycopg2
from dotenv import load_dotenv

load_dotenv()

user = os.getenv("POSTGRES_USER", "postgres")
password = os.getenv("POSTGRES_PASSWORD", "")
host = os.getenv("POSTGRES_HOST", "localhost")
port = os.getenv("POSTGRES_PORT", "5432")
database = os.getenv("POSTGRES_DB", "postgres")

connection = psycopg2.connect(
    user=user, password=password, host=host, port=port, database=database
)

cursor = connection.cursor()

# Enable pgcrypto for gen_random_uuid()
try:
    cursor.execute("CREATE EXTENSION IF NOT EXISTS pgcrypto;")
    print("✅ pgcrypto extension created/verified")
except Exception as e:
    print(f"⚠️  pgcrypto extension issue (may already exist): {e}")

# Create pgvector extension if not exists (skip if insufficient privileges)
try:
    cursor.execute("CREATE EXTENSION IF NOT EXISTS vector;")
    print("✅ vector extension created/verified")
except Exception as e:
    print(f"⚠️  vector extension issue (may need admin privileges): {e}")
    # Rollback the failed transaction
    connection.rollback()

# Create fuzzymatch extension if not exists
try:
    cursor.execute("CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;")
    print("✅ fuzzystrmatch extension created/verified")
except Exception as e:
    print(f"⚠️  fuzzystrmatch extension issue (may already exist): {e}")
    # Rollback the failed transaction
    connection.rollback()


# Projects table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_name TEXT,
    name TEXT,
    description TEXT,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
"""
)

# Candidates table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS candidates_smarthr (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proj_id UUID REFERENCES projects(id),
    candidate_info JSONB,
    suggested_positions JSONB,
    analysis_status TEXT,
    to_be_embebbed TEXT,
    embedding VECTOR(1536),
    Reason_Info JSONB,
    is_active BOOLEAN DEFAULT true,
    is_deleted BOOLEAN DEFAULT false,
    last_matching TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT NOW(),
    updated_by TEXT
);
"""
)

# add columns is_deleted, created_by, updated_by to candidates_smarthr table if not exists
cursor.execute(
    """
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'candidates_smarthr'
            AND column_name = 'is_deleted'
        ) THEN
            ALTER TABLE candidates_smarthr ADD COLUMN is_deleted BOOLEAN DEFAULT false;
        END IF;

        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'candidates_smarthr'
            AND column_name = 'created_by'
        ) THEN
            ALTER TABLE candidates_smarthr ADD COLUMN created_by TEXT;
        END IF;

        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'candidates_smarthr'
            AND column_name = 'updated_by'
        ) THEN
            ALTER TABLE candidates_smarthr ADD COLUMN updated_by TEXT;
        END IF;
    END $$;
    """
)


# Positions table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS positions_smarthr (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proj_id UUID REFERENCES projects(id),
    position_info JSONB,
    top_candidates JSONB,
    to_be_embebbed TEXT,
    embedding VECTOR(1536),
    last_matching TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    priority_skills JSONB,
    external_id TEXT
);
"""
)

# Ideal candidates table with UUID primary key
# Try with VECTOR type first, fallback to TEXT if vector extension is not available
try:
    cursor.execute(
        """
    CREATE TABLE IF NOT EXISTS ideal_candidates_smarthr (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        position_id UUID,
        proj_id UUID,
        ideal_candidate_info JSONB,
        generation_prompt TEXT,
        generation_model TEXT,
        to_be_embedded TEXT,
        embedding VECTOR(1536),
        embedding_generated BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        created_by TEXT,
        updated_at TIMESTAMP DEFAULT NOW(),
        updated_by TEXT
    );
    """
    )
    print("✅ ideal_candidates_smarthr table created with VECTOR type")
except Exception as e:
    print(f"⚠️  Failed to create table with VECTOR type: {e}")
    # Rollback the failed transaction
    connection.rollback()
    print("🔄 Trying to create table with TEXT type for embedding...")
    try:
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS ideal_candidates_smarthr (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            position_id UUID,
            proj_id UUID,
            ideal_candidate_info JSONB,
            generation_prompt TEXT,
            generation_model TEXT,
            to_be_embedded TEXT,
            embedding TEXT,
            embedding_generated BOOLEAN DEFAULT false,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT NOW(),
            created_by TEXT,
            updated_at TIMESTAMP DEFAULT NOW(),
            updated_by TEXT
        );
        """
        )
        print("✅ ideal_candidates_smarthr table created with TEXT type for embedding")
    except Exception as e2:
        print(f"❌ Failed to create ideal_candidates_smarthr table: {e2}")
        # Rollback the failed transaction
        connection.rollback()

# Create index on position_id for faster lookups
try:
    cursor.execute(
        """
    CREATE INDEX IF NOT EXISTS idx_ideal_candidates_position_id
    ON ideal_candidates_smarthr(position_id);
    """
    )
    print("✅ Index on position_id created")
except Exception as e:
    print(f"⚠️  Failed to create index on position_id: {e}")
    connection.rollback()

# Create index on proj_id for faster lookups
try:
    cursor.execute(
        """
    CREATE INDEX IF NOT EXISTS idx_ideal_candidates_proj_id
    ON ideal_candidates_smarthr(proj_id);
    """
    )
    print("✅ Index on proj_id created")
except Exception as e:
    print(f"⚠️  Failed to create index on proj_id: {e}")
    connection.rollback()

# add column external_id to positions_smarthr table if not exists
try:
    cursor.execute(
        """
    DO $$
    BEGIN
        IF NOT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_name = 'positions_smarthr'
                AND column_name = 'priority_skills'
            ) THEN
                ALTER TABLE positions_smarthr ADD COLUMN priority_skills JSONB;
        END IF;
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'positions_smarthr'
            AND column_name = 'external_id'
        ) THEN
            ALTER TABLE positions_smarthr ADD COLUMN external_id TEXT;
        END IF;
    END $$;
    """
    )
    print("✅ positions_smarthr columns added")
except Exception as e:
    print(f"⚠️  Failed to add columns to positions_smarthr: {e}")
    connection.rollback()


# Create indexes
cursor.execute(
    """
CREATE INDEX IF NOT EXISTS candidates_embedding_idx 
ON candidates_smarthr USING hnsw (embedding vector_cosine_ops);
"""
)

# cursor.execute(
#     """
# CREATE INDEX IF NOT EXISTS positions_embedding_idx 
# ON positions_smarthr USING hnsw (embedding vector_cosine_ops);
# """
# )

# cursor.execute(
#     """
# CREATE INDEX idx_candidates_personal_info_email 
# ON candidates_smarthr USING GIN ((candidate_info->'personal_info'->>'email'));

# """
# )

# cursor.execute(
#     """
# CREATE INDEX idx_candidates_personal_info_full_name 
# ON candidates_smarthr USING GIN ((candidate_info->'personal_info'->>'full_name'));
# """
# )


# -------------------- ENUM interview_status ------------------------
cursor.execute(
    """
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'interview_status') THEN
        CREATE TYPE interview_status AS ENUM
            ('scheduled', 'in_progress', 'completed', 'cancelled', 'not_scheduled');
    ELSE
        -- Add missing enum values if they don't exist
        IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'not_scheduled' AND enumtypid = 'interview_status'::regtype) THEN
            ALTER TYPE interview_status ADD VALUE IF NOT EXISTS 'not_scheduled';
        END IF;
    END IF;
END $$;
"""
)

# ------------------ TABLE interview_questions ----------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS interview_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL
               REFERENCES positions_smarthr(id) ON DELETE CASCADE,
    data JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE (position_id)
);
"""
)

# ----------------------- TABLE interviews --------------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS interviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    position_id  UUID NOT NULL
                 REFERENCES positions_smarthr(id) ON DELETE CASCADE,
    candidate_id UUID NOT NULL
                 REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
    analysis_data JSONB,
    feedback_hr        JSONB,
    interview_date_hr  TIMESTAMP,
    feedback_date_hr   TIMESTAMP,
    recruiter_hr_id    TEXT,
    scheduled_hr_id    TEXT,
    status_hr          interview_status,
    recommendation_hr  BOOLEAN,
    transcript_hr      TEXT,

    feedback_tec       JSONB,
    recruiter_tec_id   TEXT,
    scheduled_tec_id   TEXT,
    interview_date_tec TIMESTAMP,
    feedback_date_tec  TIMESTAMP,
    status_tec         interview_status,
    recommendation_tec BOOLEAN,
    transcript_tec     TEXT,

    anwers_data   JSONB,
    interview_data JSONB,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
"""
)

# add column is_deleted to interviews table
cursor.execute(
    """
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'interviews' 
            AND column_name = 'recruiter_hr_id'
        ) THEN
            ALTER TABLE interviews ADD COLUMN recruiter_hr_id TEXT;
            ALTER TABLE interviews ADD COLUMN scheduled_hr_id TEXT;
            ALTER TABLE interviews ADD COLUMN recruiter_tec_id TEXT;
            ALTER TABLE interviews ADD COLUMN scheduled_tec_id TEXT;
        END IF;
    """
)

# add column analysis_data to interviews table
cursor.execute(
    """
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'interviews' 
            AND column_name = 'analysis_data'
        ) THEN
            ALTER TABLE interviews ADD COLUMN analysis_data JSONB;
        END IF;
    """
)

# ----------------------- TABLE candidate_notes ------------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS candidate_notes (
    id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    candidate_id    UUID NOT NULL REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
    notes           JSONB,
    created_by      TEXT,
    created_at      TIMESTAMP DEFAULT NOW(),
    updated_by      TEXT,
    updated_at      TIMESTAMP DEFAULT NOW()
);
"""
)

# ----------------------- TRIGGER updated_at ------------------------
cursor.execute(
    """
CREATE OR REPLACE FUNCTION trg_set_updated_at()
RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;
"""
)

cursor.execute("DROP TRIGGER IF EXISTS set_updated_at_interviews ON interviews;")
cursor.execute(
    """
CREATE TRIGGER set_updated_at_interviews
BEFORE UPDATE ON interviews
FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();
"""
)

# --------------------------- INDEXES -------------------------------
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_position   ON interviews(position_id);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_candidate  ON interviews(candidate_id);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_status_hr  ON interviews(status_hr);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_status_tec ON interviews(status_tec);"
)
connection.commit()
cursor.close()
connection.close()

print("✅ Database initialization completed successfully!")

def main():
    """Main function for database initialization"""
    print("🚀 Starting database initialization...")
    # The initialization code above will run when this module is imported or executed

if __name__ == "__main__":
    main()
