"""
Stateless ideal candidate service that generates ideal candidates on-demand without database storage.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from models.ideal_candidate import IdealCandidateGenerationResponse
from services.ideal_candidate_service import ideal_candidate_generator

logger = logging.getLogger(__name__)


class StatelessIdealCandidateService:
    """Service for generating ideal candidates without database persistence."""
    
    def __init__(self):
        self.generator = ideal_candidate_generator
    
    def generate_ideal_candidate_for_position(
        self,
        position_id: str,
        position_info: Dict[str, Any] = None
    ) -> IdealCandidateGenerationResponse:
        """
        Generate an ideal candidate for a position (stateless - no database storage).

        Args:
            position_id: The position ID to generate ideal candidate for
            position_info: Optional position information. If not provided, will try to fetch from DB.

        Returns:
            Response containing the generated ideal candidate or error information
        """
        start_time = datetime.now()

        try:
            logger.info(f"Generating stateless ideal candidate for position {position_id}")

            # Try to get position info from database if not provided
            if not position_info:
                position_info = self._get_position_info(position_id)

            # If we still don't have position info, create a more intelligent fallback
            if not position_info:
                logger.warning(f"Could not retrieve position info for {position_id}, using intelligent fallback")
                position_info = self._create_intelligent_fallback_position(position_id)
            
            # Prepare position text for the LLM
            position_text = self._prepare_position_text(position_info)
            
            # Generate ideal candidate using LLM (no database storage)
            generation_result = self.generator.generate_ideal_candidate_from_position(
                position_text=position_text,
                position_info=position_info,
                generation_options=None,
                model_preference=None
            )
            
            generation_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
            
            if not generation_result["generation_success"]:
                logger.warning(f"LLM generation failed, using fallback profile")
                # Return fallback profile when LLM fails
                fallback_profile = self._generate_fallback_profile(position_info)
                
                return IdealCandidateGenerationResponse(
                    ideal_candidate_id="",  # No ID since we're not storing it
                    position_id=position_id,
                    ideal_candidate_info=fallback_profile,
                    generation_success=False,
                    generation_time_ms=generation_time_ms,
                    error_message=f"LLM generation failed. Using fallback profile. Error: {generation_result.get('error_message', 'Unknown error')}"
                )
            
            # Return the generated ideal candidate without storing it
            return IdealCandidateGenerationResponse(
                ideal_candidate_id="",  # No ID since we're not storing it
                position_id=position_id,
                ideal_candidate_info=generation_result["ideal_candidate_info"],
                generation_success=True,
                generation_time_ms=generation_time_ms
            )
            
        except Exception as e:
            generation_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
            logger.error(f"Error in stateless ideal candidate generation: {e}")
            
            # Return fallback profile on any error
            fallback_profile = self._generate_fallback_profile(position_info or {})
            
            return IdealCandidateGenerationResponse(
                ideal_candidate_id="",
                position_id=position_id,
                ideal_candidate_info=fallback_profile,
                generation_success=False,
                generation_time_ms=generation_time_ms,
                error_message=f"Generation failed: {str(e)}. Using fallback profile."
            )
    
    def _prepare_position_text(self, position_info: Dict[str, Any]) -> str:
        """
        Prepare position text for LLM processing.
        
        Args:
            position_info: The position information dictionary
            
        Returns:
            Formatted position text
        """
        title = position_info.get('title', 'Professional')
        description = position_info.get('description', '')
        requirements = position_info.get('requirements', [])
        responsibilities = position_info.get('responsibilities', [])
        
        text_parts = [f"Position: {title}"]
        
        if description:
            text_parts.append(f"Description: {description}")
        
        if requirements:
            req_text = ", ".join(requirements) if isinstance(requirements, list) else str(requirements)
            text_parts.append(f"Requirements: {req_text}")
        
        if responsibilities:
            resp_text = ", ".join(responsibilities) if isinstance(responsibilities, list) else str(responsibilities)
            text_parts.append(f"Responsibilities: {resp_text}")
        
        return " | ".join(text_parts)
    
    def _generate_fallback_profile(self, position_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a position-specific fallback ideal candidate profile in candidate JSON format.

        Args:
            position_info: The position information to base the profile on

        Returns:
            A position-specific ideal candidate profile in candidate format
        """
        position_title = position_info.get('title', 'Software Engineer')
        requirements = position_info.get('requirements', [])
        responsibilities = position_info.get('responsibilities', [])
        description = position_info.get('description', '')

        # Extract technical skills from requirements if available
        tech_skills_data = self._extract_technical_skills(requirements, position_title)
        soft_skills_data = self._extract_soft_skills(requirements, responsibilities)

        # Convert to candidate format skills
        skills = []
        for tech in tech_skills_data['core_technologies'][:5]:
            skills.append({
                "name": tech,
                "proficiency_level": "Advanced",
                "years_of_experience": 5
            })

        for framework in tech_skills_data['frameworks_tools'][:3]:
            skills.append({
                "name": framework,
                "proficiency_level": "Intermediate",
                "years_of_experience": 3
            })

        # Convert soft skills
        soft_skills = []
        for skill in soft_skills_data[:5]:
            soft_skills.append({
                "name": skill,
                "description": ""
            })

        return {
            "roles": [position_title, f"Senior {position_title}", f"{position_title} Lead"],
            "response": {
                "roles": None,
                "skills": skills,
                "summary": f"Experienced {position_title} with {5} years of expertise in {', '.join(tech_skills_data['core_technologies'][:3]) if tech_skills_data['core_technologies'] else 'relevant technologies'}. Proven track record of delivering high-quality solutions and leading technical initiatives. {description[:150] + '...' if len(description) > 150 else description}",
                "projects": None,
                "education": [
                    {
                        "degree": "Bachelor's Degree" if 'engineer' in position_title.lower() else "Bachelor's Degree",
                        "end_date": "31 Dec 2020",
                        "location": "",
                        "start_date": "01 Sep 2016",
                        "description": "",
                        "field_of_study": "Computer Science" if 'engineer' in position_title.lower() or 'developer' in position_title.lower() else "Relevant Field",
                        "institution_name": "University"
                    }
                ],
                "languages": [
                    {
                        "language": "English",
                        "proficiency_level": "C1"
                    }
                ],
                "references": None,
                "soft_skills": soft_skills,
                "personal_info": {
                    "city": "",
                    "email": "",
                    "address": "",
                    "country": "",
                    "website": "",
                    "full_name": f"Ideal {position_title}",
                    "phone_number": "",
                    "linkedin_profile": ""
                },
                "certifications": None,
                "work_experience": self._generate_work_experience(position_title, tech_skills_data, responsibilities)
            }
        }

    def _generate_work_experience(self, position_title: str, tech_skills_data: Dict, responsibilities: list) -> list:
        """Generate realistic work experience for the ideal candidate."""

        # Get main technologies for this position
        main_techs = tech_skills_data['core_technologies'][:3]

        # Generate 2-3 work experiences
        experiences = []

        # Current/Recent position
        current_skills = []
        for tech in main_techs:
            current_skills.append({
                "name": tech,
                "proficiency_level": "Advanced",
                "years_of_experience": 3
            })

        current_responsibilities = responsibilities[:4] if responsibilities else [
            f"Lead {position_title.lower()} development initiatives and technical architecture decisions",
            f"Mentor junior {position_title.lower()}s and conduct code reviews",
            "Collaborate with cross-functional teams to deliver high-quality solutions",
            "Implement best practices and optimize system performance"
        ]

        experiences.append({
            "skills": current_skills,
            "end_date": "Currently working",
            "location": "",
            "job_title": f"Senior {position_title}",
            "start_date": "01 Jan 2022",
            "company_name": "Tech Company",
            "responsibilities": current_responsibilities
        })

        # Previous position
        prev_skills = []
        for tech in main_techs[1:3]:  # Different subset of skills
            prev_skills.append({
                "name": tech,
                "proficiency_level": "Intermediate",
                "years_of_experience": 2
            })

        experiences.append({
            "skills": prev_skills,
            "end_date": "31 Dec 2021",
            "location": "",
            "job_title": position_title,
            "start_date": "01 Jun 2019",
            "company_name": "Previous Company",
            "responsibilities": [
                f"Developed and maintained {position_title.lower()} applications",
                "Participated in agile development processes",
                "Collaborated with QA team to ensure quality deliverables",
                "Contributed to technical documentation and knowledge sharing"
            ]
        })

        return experiences

    def _get_position_info(self, position_id: str) -> Dict[str, Any]:
        """
        Try to get position information from the database.

        Args:
            position_id: The position ID to look up

        Returns:
            Position information dictionary or None if not found
        """
        try:
            # Import here to avoid circular imports
            from controllers.position_controller import get_position_by_id

            position = get_position_by_id(position_id)
            if position and hasattr(position, 'position_info'):
                logger.info(f"Successfully retrieved position info for {position_id}")
                return position.position_info
            else:
                logger.warning(f"Position {position_id} not found or has no position_info")
                return None

        except Exception as e:
            logger.warning(f"Failed to retrieve position info for {position_id}: {e}")
            return None

    def _create_intelligent_fallback_position(self, position_id: str) -> Dict[str, Any]:
        """
        Create a more intelligent fallback position based on the position_id.

        Args:
            position_id: The position ID to base the fallback on

        Returns:
            Intelligent fallback position information
        """
        # Try to infer some information from the position_id or create reasonable defaults
        # This is a fallback when we can't access the database

        return {
            "title": "Software Engineer",  # Common default
            "description": f"We are seeking a skilled Software Engineer to join our dynamic team. This role involves developing high-quality software solutions and collaborating with cross-functional teams to deliver exceptional products.",
            "requirements": [
                "Bachelor's degree in Computer Science, Software Engineering, or related field",
                "3+ years of experience in software development",
                "Proficiency in modern programming languages (Python, JavaScript, Java, etc.)",
                "Experience with web frameworks and databases",
                "Strong problem-solving and analytical skills",
                "Excellent communication and teamwork abilities",
                "Experience with version control systems (Git)",
                "Knowledge of software development best practices"
            ],
            "responsibilities": [
                "Design, develop, and maintain software applications",
                "Collaborate with product managers and designers to implement features",
                "Write clean, efficient, and well-documented code",
                "Participate in code reviews and maintain code quality standards",
                "Debug and resolve technical issues",
                "Contribute to architectural decisions and technical planning",
                "Stay updated with emerging technologies and industry trends",
                "Mentor junior developers and share knowledge with the team"
            ],
            "nice_to_have": [
                "Experience with cloud platforms (AWS, Azure, GCP)",
                "Knowledge of containerization (Docker, Kubernetes)",
                "Experience with CI/CD pipelines",
                "Understanding of microservices architecture",
                "Experience with agile development methodologies"
            ],
            "company_info": {
                "type": "Technology Company",
                "size": "Medium to Large",
                "culture": "Collaborative, innovative, and growth-oriented"
            }
        }

    def _extract_technical_skills(self, requirements: list, position_title: str) -> Dict[str, list]:
        """Extract technical skills from requirements and position title."""

        # Default skills based on position title
        if 'engineer' in position_title.lower() or 'developer' in position_title.lower():
            core_tech = ["Python", "JavaScript", "React", "Node.js", "SQL"]
            frameworks = ["Django", "Flask", "Express.js", "React", "Vue.js"]
            languages = ["Python", "JavaScript", "TypeScript", "Java"]
            databases = ["PostgreSQL", "MySQL", "MongoDB", "Redis"]
            cloud = ["AWS", "Azure", "Docker", "Kubernetes"]
        elif 'data' in position_title.lower():
            core_tech = ["Python", "SQL", "Pandas", "NumPy", "Scikit-learn"]
            frameworks = ["TensorFlow", "PyTorch", "Apache Spark", "Jupyter"]
            languages = ["Python", "R", "SQL", "Scala"]
            databases = ["PostgreSQL", "MongoDB", "Snowflake", "BigQuery"]
            cloud = ["AWS", "GCP", "Azure", "Databricks"]
        elif 'product' in position_title.lower():
            core_tech = ["Analytics tools", "A/B testing", "User research", "Roadmapping"]
            frameworks = ["Jira", "Confluence", "Figma", "Miro"]
            languages = ["SQL", "Basic Python"]
            databases = ["Analytics databases", "Data warehouses"]
            cloud = ["Cloud platforms", "SaaS tools"]
        else:
            core_tech = ["Relevant technologies", "Industry-standard tools"]
            frameworks = ["Popular frameworks", "Development tools"]
            languages = ["Programming languages", "Scripting languages"]
            databases = ["Database systems", "Data storage"]
            cloud = ["Cloud platforms", "DevOps tools"]

        # Try to extract specific technologies from requirements
        req_text = ' '.join(requirements).lower() if requirements else ''

        # Look for specific technologies mentioned in requirements
        tech_keywords = {
            'python': 'Python', 'javascript': 'JavaScript', 'java': 'Java', 'react': 'React',
            'node': 'Node.js', 'django': 'Django', 'flask': 'Flask', 'aws': 'AWS', 'azure': 'Azure',
            'docker': 'Docker', 'kubernetes': 'Kubernetes', 'sql': 'SQL', 'postgresql': 'PostgreSQL',
            'mongodb': 'MongoDB', 'redis': 'Redis', 'git': 'Git', 'tensorflow': 'TensorFlow',
            'pytorch': 'PyTorch', 'pandas': 'Pandas', 'numpy': 'NumPy'
        }

        found_tech = []
        for keyword, tech_name in tech_keywords.items():
            if keyword in req_text:
                found_tech.append(tech_name)

        # Use found technologies or defaults
        if found_tech:
            core_tech = found_tech + [t for t in core_tech if t not in found_tech][:5]

        return {
            "core_technologies": core_tech[:5],
            "frameworks_tools": frameworks[:4],
            "programming_languages": languages[:3],
            "databases": databases[:3],
            "cloud_platforms": cloud[:3],
            "other_technical": ["Version control (Git)", "Agile methodologies", "Testing frameworks"]
        }

    def _extract_soft_skills(self, requirements: list, responsibilities: list) -> list:
        """Extract relevant soft skills from requirements and responsibilities."""

        all_text = ' '.join(requirements + responsibilities).lower() if requirements or responsibilities else ''

        soft_skills = ["Communication", "Problem-solving", "Team collaboration"]

        skill_keywords = {
            'leadership': 'Leadership',
            'mentor': 'Mentoring',
            'communication': 'Communication',
            'collaboration': 'Team collaboration',
            'problem': 'Problem-solving',
            'analytical': 'Analytical thinking',
            'creative': 'Creativity',
            'adaptable': 'Adaptability',
            'time management': 'Time management',
            'project management': 'Project management',
            'critical thinking': 'Critical thinking'
        }

        for keyword, skill in skill_keywords.items():
            if keyword in all_text and skill not in soft_skills:
                soft_skills.append(skill)

        return soft_skills[:6]  # Limit to 6 skills

    def _get_relevant_certifications(self, position_title: str) -> list:
        """Get relevant certifications based on position title."""

        if 'engineer' in position_title.lower() or 'developer' in position_title.lower():
            return ["AWS Certified Developer", "Professional Scrum Master", "Relevant technical certifications"]
        elif 'data' in position_title.lower():
            return ["AWS Certified Data Analytics", "Google Cloud Professional Data Engineer", "Certified Analytics Professional"]
        elif 'product' in position_title.lower():
            return ["Certified Scrum Product Owner", "Product Management Certificate", "Google Analytics Certified"]
        else:
            return ["Industry-relevant certifications", "Professional development certificates"]


# Global instance
stateless_ideal_candidate_service = StatelessIdealCandidateService()
