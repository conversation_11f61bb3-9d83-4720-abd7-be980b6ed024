"""
Test script for ideal candidate functionality.
"""

import sys
import os
import json
import logging
from typing import Dict, Any

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.ideal_candidate import IdealCandidateGenerationRequest
from services.ideal_candidate_workflow import ideal_candidate_workflow
from services.ideal_candidate_embedding_service import ideal_candidate_embedding_service
from controllers.ideal_candidate_controller import (
    get_ideal_candidate_by_position_id,
    get_ideal_candidates_by_filters
)
from utils.ideal_candidate_text_utils import (
    prepare_ideal_candidate_for_embedding,
    validate_ideal_candidate_structure
)
from utils.ideal_candidate_validation import (
    validate_ideal_candidate_info_structure,
    validate_generation_request,
    sanitize_ideal_candidate_info
)
from utils.ideal_candidate_monitoring import (
    ideal_candidate_metrics,
    get_database_health_metrics,
    get_performance_summary
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_position_data() -> Dict[str, Any]:
    """Create test position data for testing."""
    return {
        "positionName": "Senior Python Developer",
        "clientName": "TechCorp Inc",
        "jobDescription": "We are looking for a Senior Python Developer to join our team. The ideal candidate will have 5+ years of experience with Python, Django, and cloud technologies.",
        "mainResponsabilities": "Develop and maintain Python applications, work with databases, collaborate with cross-functional teams, mentor junior developers.",
        "seniority": {"name": "Senior"},
        "roleName": "Python Developer",
        "projectName": "E-commerce Platform",
        "positionTypeName": "Full-time",
        "openPositionSkills": [
            {"skillName": "Python", "skillCategory": "professionalSkills", "skillLeveName": "Advanced", "skillScore": 5},
            {"skillName": "Django", "skillCategory": "professionalSkills", "skillLeveName": "Advanced", "skillScore": 5},
            {"skillName": "PostgreSQL", "skillCategory": "professionalSkills", "skillLeveName": "Intermediate", "skillScore": 4},
            {"skillName": "AWS", "skillCategory": "professionalSkills", "skillLeveName": "Intermediate", "skillScore": 4},
            {"skillName": "Docker", "skillCategory": "niceToHave", "skillLeveName": "Intermediate", "skillScore": 3}
        ]
    }


def test_ideal_candidate_generation():
    """Test ideal candidate generation functionality."""
    logger.info("Testing ideal candidate generation...")
    
    try:
        # Create a test position (this would normally exist in the database)
        test_position_id = "test-position-123"
        
        # Create generation request
        request = IdealCandidateGenerationRequest(
            position_id=test_position_id,
            generation_options={"test_mode": True},
            model_preference="gpt-4o-mini"
        )
        
        # Validate the request
        request_data = request.model_dump()
        is_valid, errors = validate_generation_request(request_data)
        
        if not is_valid:
            logger.error(f"Request validation failed: {errors}")
            return False
        
        logger.info("✓ Request validation passed")
        
        # Test the generation workflow (this would fail without a real position in DB)
        # For testing purposes, we'll create a mock ideal candidate
        mock_ideal_candidate = {
            "personal_info": {
                "professional_title": "Senior Python Developer",
                "years_of_experience": "5-7 years",
                "location_preference": "Remote or San Francisco",
                "summary": "Experienced Python developer with expertise in Django and cloud technologies"
            },
            "technical_skills": {
                "core_technologies": ["Python", "Django", "REST APIs"],
                "frameworks_tools": ["Django REST Framework", "Celery", "Redis"],
                "programming_languages": ["Python", "JavaScript", "SQL"],
                "databases": ["PostgreSQL", "MongoDB"],
                "cloud_platforms": ["AWS", "Docker"],
                "other_technical": ["Git", "CI/CD", "Microservices"]
            },
            "professional_experience": [
                {
                    "role": "Senior Python Developer",
                    "company_type": "Technology startup",
                    "duration": "3 years",
                    "key_achievements": ["Led development of e-commerce platform", "Improved system performance by 40%"],
                    "technologies_used": ["Python", "Django", "PostgreSQL", "AWS"]
                }
            ],
            "education": {
                "degree": "Bachelor's in Computer Science",
                "additional_certifications": ["AWS Certified Developer"],
                "continuous_learning": ["Advanced Python courses", "Cloud architecture training"]
            },
            "soft_skills": [
                "Leadership",
                "Problem-solving",
                "Communication",
                "Team collaboration"
            ],
            "industry_experience": {
                "domains": ["E-commerce", "Fintech"],
                "company_sizes": ["startup", "mid-size"],
                "project_types": ["web applications", "APIs"]
            },
            "leadership_management": {
                "team_leadership": "Led teams of 3-5 developers",
                "project_management": "Managed multiple concurrent projects",
                "mentoring": "Mentored 2 junior developers"
            },
            "cultural_fit": {
                "work_style": "Collaborative and results-oriented",
                "values_alignment": "Innovation and continuous learning",
                "collaboration_style": "Cross-functional team player"
            },
            "additional_qualifications": {
                "languages": ["English", "Spanish"],
                "publications": [],
                "speaking_conferences": [],
                "open_source_contributions": ["Django packages"]
            }
        }
        
        # Test ideal candidate structure validation
        is_valid, errors = validate_ideal_candidate_info_structure(mock_ideal_candidate)
        
        if not is_valid:
            logger.error(f"Ideal candidate structure validation failed: {errors}")
            return False
        
        logger.info("✓ Ideal candidate structure validation passed")
        
        # Test sanitization
        sanitized = sanitize_ideal_candidate_info(mock_ideal_candidate)
        logger.info("✓ Ideal candidate sanitization passed")
        
        # Test text preparation for embedding
        embedding_text = prepare_ideal_candidate_for_embedding(mock_ideal_candidate)
        
        if not embedding_text or len(embedding_text.strip()) == 0:
            logger.error("Failed to prepare embedding text")
            return False
        
        logger.info(f"✓ Embedding text preparation passed (length: {len(embedding_text)})")
        
        # Test embedding generation (this would require actual OpenAI API)
        # For testing, we'll just validate the structure
        logger.info("✓ Ideal candidate generation test completed successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"Ideal candidate generation test failed: {e}")
        return False


def test_text_preparation():
    """Test text preparation utilities."""
    logger.info("Testing text preparation utilities...")
    
    try:
        # Test with minimal data
        minimal_candidate = {
            "personal_info": {
                "professional_title": "Developer",
                "years_of_experience": "3 years"
            },
            "technical_skills": {
                "core_technologies": ["Python"]
            },
            "professional_experience": [
                {
                    "role": "Developer",
                    "duration": "2 years"
                }
            ],
            "education": {
                "degree": "Computer Science"
            },
            "soft_skills": ["Communication"]
        }
        
        # Test text preparation
        text = prepare_ideal_candidate_for_embedding(minimal_candidate)
        
        if not text or len(text.strip()) == 0:
            logger.error("Failed to prepare text for minimal candidate")
            return False
        
        logger.info("✓ Minimal candidate text preparation passed")
        
        # Test with empty data
        empty_candidate = {}
        text = prepare_ideal_candidate_for_embedding(empty_candidate)
        
        # Should handle empty data gracefully
        if text is None:
            logger.error("Text preparation should handle empty data gracefully")
            return False
        
        logger.info("✓ Empty candidate text preparation handled gracefully")
        
        return True
        
    except Exception as e:
        logger.error(f"Text preparation test failed: {e}")
        return False


def test_validation_utilities():
    """Test validation utilities."""
    logger.info("Testing validation utilities...")
    
    try:
        # Test valid UUID
        from utils.ideal_candidate_validation import validate_uuid
        
        try:
            validate_uuid("123e4567-e89b-12d3-a456-************", "test_id")
            logger.info("✓ Valid UUID validation passed")
        except Exception as e:
            logger.error(f"Valid UUID validation failed: {e}")
            return False
        
        # Test invalid UUID
        try:
            validate_uuid("invalid-uuid", "test_id")
            logger.error("Invalid UUID should have failed validation")
            return False
        except:
            logger.info("✓ Invalid UUID validation correctly failed")
        
        # Test matching parameters validation
        from utils.ideal_candidate_validation import validate_matching_parameters
        
        # Valid parameters
        is_valid, errors = validate_matching_parameters(
            position_id="123e4567-e89b-12d3-a456-************",
            limit=5,
            hasFeedback=2,
            batch_mode=True
        )
        
        if not is_valid:
            logger.error(f"Valid matching parameters failed validation: {errors}")
            return False
        
        logger.info("✓ Valid matching parameters validation passed")
        
        # Invalid parameters
        is_valid, errors = validate_matching_parameters(
            limit=150,  # Too high
            hasFeedback=5  # Invalid value
        )
        
        if is_valid:
            logger.error("Invalid matching parameters should have failed validation")
            return False
        
        logger.info("✓ Invalid matching parameters correctly failed validation")
        
        return True
        
    except Exception as e:
        logger.error(f"Validation utilities test failed: {e}")
        return False


def test_monitoring_utilities():
    """Test monitoring utilities."""
    logger.info("Testing monitoring utilities...")
    
    try:
        # Test metrics recording
        initial_metrics = ideal_candidate_metrics.get_metrics()
        
        # Record some test metrics
        ideal_candidate_metrics.record_generation_request()
        ideal_candidate_metrics.record_generation_success(1500)
        ideal_candidate_metrics.record_matching_request()
        ideal_candidate_metrics.record_matching_success(800)
        
        # Get updated metrics
        updated_metrics = ideal_candidate_metrics.get_metrics()
        
        # Verify metrics were recorded
        if updated_metrics["generation_requests"] <= initial_metrics["generation_requests"]:
            logger.error("Generation request metric not recorded")
            return False
        
        if updated_metrics["generation_successes"] <= initial_metrics["generation_successes"]:
            logger.error("Generation success metric not recorded")
            return False
        
        logger.info("✓ Metrics recording test passed")
        
        # Test performance summary
        summary = get_performance_summary(24)
        
        if "generation_metrics" not in summary:
            logger.error("Performance summary missing generation metrics")
            return False
        
        logger.info("✓ Performance summary test passed")
        
        return True
        
    except Exception as e:
        logger.error(f"Monitoring utilities test failed: {e}")
        return False


def run_all_tests():
    """Run all ideal candidate functionality tests."""
    logger.info("Starting ideal candidate functionality tests...")
    
    tests = [
        ("Ideal Candidate Generation", test_ideal_candidate_generation),
        ("Text Preparation", test_text_preparation),
        ("Validation Utilities", test_validation_utilities),
        ("Monitoring Utilities", test_monitoring_utilities)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.error(f"💥 {total - passed} tests failed!")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
