# PowerShell script to run database initialization

Write-Host "Running database initialization..." -ForegroundColor Green

# Check if Dock<PERSON> is running
try {
    docker --version | Out-Null
    Write-Host "✅ Docker is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not available or not running" -ForegroundColor Red
    exit 1
}

# Check if docker-compose is available
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available" -ForegroundColor Red
    exit 1
}

Write-Host "Building Docker image..." -ForegroundColor Yellow
docker-compose build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to build Docker image" -ForegroundColor Red
    exit 1
}

Write-Host "Running database initialization through Docker..." -ForegroundColor Yellow
docker-compose run --rm smarthr python scripts/init_db.py

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Database initialization completed successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Database initialization failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Database initialization process completed." -ForegroundColor Green
