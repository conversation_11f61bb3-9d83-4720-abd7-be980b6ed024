# This file provides a prompt template for evaluating a candidate against a position.
# The LLM must return <PERSON><PERSON><PERSON> with the following fields:
# {
#   "LLM_Analysis": {...},        # <PERSON><PERSON><PERSON> with summary or reasoning
#   "extra_questions": {...},     # JSO<PERSON> with additional clarifications or interview questions suggested
#   "highlights": {...},          # JSON with key points or standout features of the candidate for this position
#   "Score": float,               # The final matching score (0 to 10)
# }
#
# Do not include "created_at" or "updated_at" in the LLM output; these are handled by code.
#

# Import prompts from langsmith
import os
from config.config import PROMPT_CONFIG

# Try to import Lang<PERSON><PERSON>, but handle gracefully if not available
try:
    from langsmith import Client
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    print("Warning: LangSmith not available. Using fallback prompts.")

class TemplatesAnalysis:
    def __init__(self):
        if LANGSMITH_AVAILABLE:
            try:
                self.client = Client()
                self.batch_prompt = self.client.pull_prompt("get_batch_candidate_analysis_batch_prompt").format()
                self.match_prompt = self.client.pull_prompt("get_candidate_analysis_prompt").format()
                self.langsmith_working = True
            except Exception as e:
                print(f"Warning: LangSmith client initialization failed: {e}")
                self.langsmith_working = False
                self._set_fallback_prompts()
        else:
            self.langsmith_working = False
            self._set_fallback_prompts()

    def _set_fallback_prompts(self):
        """Set fallback prompts when LangSmith is not available"""
        self.match_prompt = """
        You are an expert recruiter evaluating candidate-position fit.
        Analyze the candidate against the position and provide a JSON response with:
        - "LLM_Analysis": {"reason": "analysis summary", "skill_match_analysis": {}, "skill_not_matched": []}
        - "extra_questions": []
        - "highlights": []
        - "Score": float (0-100)
        """
        self.batch_prompt = """
        You are an expert recruiter evaluating multiple candidates against a position.
        Provide detailed analysis for each candidate in JSON format.
        """

    def uptate_prompts(self):
        if self.langsmith_working:
            try:
                self.batch_prompt = self.client.pull_prompt("get_batch_candidate_analysis_batch_prompt").format()
                self.match_prompt = self.client.pull_prompt("get_candidate_analysis_prompt").format()
            except Exception as e:
                print(f"Warning: Failed to update prompts from LangSmith: {e}")

templatesObject = TemplatesAnalysis()


def get_candidate_analysis_prompt():
    """Get the original LangSmith candidate analysis prompt"""
    prompt = templatesObject.match_prompt
    return prompt


def get_enhanced_candidate_analysis_prompt():
    """
    Enhanced candidate analysis prompt that focuses heavily on application and applicant details.
    Provides comprehensive analysis of candidate-position fit with detailed breakdowns.
    """
    return """
    You are an expert senior technical recruiter and talent acquisition specialist with deep expertise in evaluating candidate-position fit.
    You will receive detailed information about a candidate and a position, and you must provide a comprehensive, detailed analysis.

    Your analysis must be thorough, specific, and actionable. Focus on the following key areas:

    ## ANALYSIS REQUIREMENTS:

    ### 1. TECHNICAL SKILLS ANALYSIS
    - Analyze each technical skill mentioned in the candidate's profile against position requirements
    - Provide specific skill-level assessments (Beginner/Intermediate/Advanced/Expert)
    - Identify exact skill matches, partial matches, and gaps
    - Evaluate technology stack alignment and version compatibility where relevant
    - Assess transferable skills and learning curve for missing technologies

    ### 2. EXPERIENCE RELEVANCE EVALUATION
    - Analyze each work experience entry for relevance to the target position
    - Evaluate industry experience alignment (same industry, related industry, different industry)
    - Assess role progression and career trajectory
    - Identify specific project experiences that demonstrate required capabilities
    - Evaluate team size, project complexity, and leadership experience where applicable

    ### 3. EDUCATION AND CERTIFICATION ASSESSMENT
    - Evaluate educational background relevance to position requirements
    - Assess the value of certifications in the context of the role
    - Consider continuous learning indicators and professional development
    - Evaluate academic achievements and their relevance to practical application

    ### 4. SENIORITY AND RESPONSIBILITY ALIGNMENT
    - Compare candidate's current seniority level with position requirements
    - Analyze scope of responsibilities in previous roles vs. target role
    - Evaluate readiness for increased/decreased responsibility
    - Assess leadership experience and team management capabilities

    ### 5. SOFT SKILLS AND CULTURAL FIT
    - Evaluate communication skills based on available information
    - Assess collaboration and teamwork indicators
    - Analyze problem-solving approach and methodology preferences
    - Consider adaptability and learning agility indicators

    ### 6. LOCATION AND LOGISTICS
    - Evaluate location compatibility with position requirements
    - Assess remote work experience if applicable
    - Consider time zone alignment for distributed teams
    - Evaluate travel requirements compatibility

    ## OUTPUT REQUIREMENTS:

    Return a JSON object with the following structure:

    {
        "LLM_Analysis": {
            "reason": "Comprehensive summary of the overall candidate-position fit assessment",
            "skill_match_analysis": {
                "technical_skills": {
                    "matched_skills": ["skill1: assessment and relevance", "skill2: assessment and relevance"],
                    "partial_matches": ["skill1: gap analysis and learning curve", "skill2: gap analysis"],
                    "transferable_skills": ["skill1: how it transfers to required skill", "skill2: transfer potential"]
                },
                "experience_analysis": {
                    "relevant_experience": ["experience1: specific relevance and value", "experience2: relevance"],
                    "industry_alignment": "detailed assessment of industry experience fit",
                    "role_progression": "analysis of career trajectory and growth pattern",
                    "project_complexity": "evaluation of project scale and complexity handled"
                },
                "education_assessment": {
                    "degree_relevance": "how educational background supports the role",
                    "certifications_value": "assessment of certifications and their current relevance",
                    "continuous_learning": "indicators of ongoing professional development"
                },
                "seniority_fit": {
                    "level_alignment": "assessment of seniority level match",
                    "responsibility_readiness": "evaluation of readiness for role responsibilities",
                    "leadership_capability": "assessment of leadership and mentoring potential"
                }
            },
            "skill_not_matched": ["specific skill gaps that need to be addressed", "missing requirements"],
            "risk_assessment": {
                "high_risk_areas": ["areas of concern that could impact success"],
                "mitigation_strategies": ["specific approaches to address identified risks"],
                "success_probability": "assessment of likelihood of success in the role"
            },
            "growth_potential": {
                "learning_trajectory": "expected learning curve and development path",
                "advancement_potential": "potential for growth within the organization",
                "skill_development_areas": ["areas where candidate could develop further"]
            }
        },
        "extra_questions": [
            "Specific technical questions to validate skill claims",
            "Behavioral questions to assess soft skills and cultural fit",
            "Scenario-based questions to evaluate problem-solving approach",
            "Questions to clarify experience gaps or concerns",
            "Questions to assess motivation and career goals alignment"
        ],
        "highlights": [
            "Key strengths that make this candidate particularly suitable",
            "Unique value propositions the candidate brings",
            "Standout achievements or experiences relevant to the role",
            "Competitive advantages over typical candidates",
            "Specific reasons why this candidate should be prioritized"
        ],
        "Score": 85.5
    }

    ## SCORING GUIDELINES:
    - Score range: 0-100 (use decimal precision, e.g., 85.5, 72.3)
    - 90-100: Exceptional fit, rare to find better match
    - 80-89: Strong fit, highly recommended with minor gaps
    - 70-79: Good fit, recommended with some development needed
    - 60-69: Moderate fit, viable with significant training/support
    - 50-59: Weak fit, considerable gaps but potential exists
    - Below 50: Poor fit, not recommended for this specific role

    ## IMPORTANT GUIDELINES:
    - Be specific and detailed in all assessments
    - Use concrete examples from the candidate's background when possible
    - Avoid generic statements; provide actionable insights
    - Consider both current capabilities and growth potential
    - Balance optimism with realistic assessment of gaps and challenges
    - Provide practical recommendations for interview focus areas
    - Consider the complete candidate profile, not just individual components

    Return only valid JSON. No additional commentary outside the JSON structure.
    """


def get_batch_candidate_analysis_batch_prompt():
    """Get the original LangSmith batch candidate analysis prompt"""
    prompt = templatesObject.batch_prompt
    return prompt


def get_candidate_analysis_prompt_smart():
    """
    Smart function that returns either the original or enhanced candidate analysis prompt
    based on configuration settings.
    """
    if PROMPT_CONFIG.get('enhanced_candidate_analysis', True):
        return get_enhanced_candidate_analysis_prompt()
    else:
        return get_candidate_analysis_prompt()


def get_enhanced_batch_candidate_analysis_prompt():
    """
    Enhanced batch candidate analysis prompt for evaluating multiple candidates
    with detailed focus on application and applicant details.
    """
    return """
    You are an expert senior technical recruiter and talent acquisition specialist with deep expertise in evaluating multiple candidates against a single position.
    You will receive detailed information about multiple candidates and one position, and you must provide comprehensive, detailed analysis for each candidate.

    Your analysis must be thorough, specific, and actionable for each candidate. Focus on the same detailed areas as individual analysis but in a comparative context.

    ## ANALYSIS REQUIREMENTS FOR EACH CANDIDATE:

    ### 1. INDIVIDUAL DETAILED ANALYSIS
    For each candidate, provide the same comprehensive analysis as the individual prompt:
    - Technical skills analysis with specific skill-level assessments
    - Experience relevance evaluation with industry and role alignment
    - Education and certification assessment
    - Seniority and responsibility alignment
    - Soft skills and cultural fit evaluation
    - Location and logistics compatibility

    ### 2. COMPARATIVE ANALYSIS
    - Rank candidates based on overall fit
    - Identify unique strengths each candidate brings
    - Compare skill sets and experience levels
    - Assess complementary capabilities across the candidate pool

    ## OUTPUT REQUIREMENTS:

    Return a JSON object with the following structure:

    {
        "candidates_analysis": [
            {
                "candidate_index": 0,
                "LLM_Analysis": {
                    "reason": "Comprehensive summary of candidate-position fit",
                    "skill_match_analysis": {
                        "technical_skills": {
                            "matched_skills": ["detailed skill assessments"],
                            "partial_matches": ["gap analysis and learning curve"],
                            "transferable_skills": ["transfer potential analysis"]
                        },
                        "experience_analysis": {
                            "relevant_experience": ["specific relevance and value"],
                            "industry_alignment": "detailed industry fit assessment",
                            "role_progression": "career trajectory analysis",
                            "project_complexity": "project scale evaluation"
                        },
                        "education_assessment": {
                            "degree_relevance": "educational background support",
                            "certifications_value": "certification relevance assessment",
                            "continuous_learning": "professional development indicators"
                        },
                        "seniority_fit": {
                            "level_alignment": "seniority level match assessment",
                            "responsibility_readiness": "role responsibility evaluation",
                            "leadership_capability": "leadership potential assessment"
                        }
                    },
                    "skill_not_matched": ["specific skill gaps"],
                    "risk_assessment": {
                        "high_risk_areas": ["areas of concern"],
                        "mitigation_strategies": ["risk mitigation approaches"],
                        "success_probability": "success likelihood assessment"
                    },
                    "growth_potential": {
                        "learning_trajectory": "expected development path",
                        "advancement_potential": "growth potential within organization",
                        "skill_development_areas": ["development opportunities"]
                    },
                    "comparative_advantages": ["unique strengths vs other candidates"],
                    "competitive_positioning": "how this candidate stands out"
                },
                "extra_questions": [
                    "Specific technical validation questions",
                    "Behavioral and cultural fit questions",
                    "Scenario-based problem-solving questions",
                    "Experience clarification questions",
                    "Motivation and goal alignment questions"
                ],
                "highlights": [
                    "Key strengths for this specific position",
                    "Unique value propositions",
                    "Standout achievements relevant to role",
                    "Competitive advantages",
                    "Priority reasons for consideration"
                ],
                "Score": 85.5
            }
        ],
        "batch_summary": {
            "candidate_ranking": [
                {
                    "candidate_index": 0,
                    "score": 85.5,
                    "primary_strengths": ["key advantages"],
                    "main_concerns": ["primary risks or gaps"]
                }
            ],
            "diversity_analysis": {
                "skill_coverage": "how candidates complement each other",
                "experience_variety": "range of backgrounds represented",
                "seniority_distribution": "mix of experience levels"
            },
            "hiring_recommendations": {
                "top_candidates": ["indices of highest recommended candidates"],
                "interview_priorities": ["focus areas for interviews"],
                "decision_factors": ["key criteria for final selection"]
            }
        }
    }

    ## SCORING AND RANKING GUIDELINES:
    - Use the same 0-100 scoring scale as individual analysis
    - Provide clear ranking rationale
    - Consider both individual fit and team composition
    - Balance current capabilities with growth potential
    - Account for diversity of skills and perspectives

    ## IMPORTANT GUIDELINES:
    - Maintain detailed analysis quality for each candidate
    - Provide actionable comparative insights
    - Focus on specific, measurable differences
    - Consider team dynamics and complementary skills
    - Provide clear hiring recommendations with rationale

    Return only valid JSON. No additional commentary outside the JSON structure.
    """
