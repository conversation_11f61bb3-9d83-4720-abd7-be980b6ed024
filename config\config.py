# config.py
import os

DATABASE_CONFIG = {
    'user': os.getenv('DB_USER', 'default_user'),
    'password': os.getenv('DB_PASSWORD', 'default_password'),
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'database': os.getenv('DB_NAME', 'postgres'),
}

CANDIDATE_RESUME_PAGE_OPTIONS = {
    'page-size': 'A4',
    'margin-top': '0.25in',
    'margin-right': '0.75in',
    'margin-bottom': '0.75in',
    'margin-left': '0.75in',
    'encoding': "UTF-8",
    'no-outline': None
}


MODELS_CONFIG = {
    'position_matching_models_order': eval(os.getenv('POSITION_MATCH_MODELS_ORDER', '["llama4-pro","llama4-light","gpt-4o", "gpt-4o-mini", "llama33-70b"]')),
    'ideal_candidate_models_order': eval(os.getenv('IDEAL_CANDIDATE_MODELS_ORDER', '["gpt-4o", "gpt-4o-mini", "llama4-pro", "llama4-light"]'))
}

# Prompt configuration - allows switching between original and enhanced prompts
PROMPT_CONFIG = {
    'use_enhanced_prompts': os.getenv('USE_ENHANCED_PROMPTS', 'true').lower() == 'true',
    'enhanced_candidate_analysis': os.getenv('ENHANCED_CANDIDATE_ANALYSIS', 'true').lower() == 'true',
    'enhanced_position_analysis': os.getenv('ENHANCED_POSITION_ANALYSIS', 'true').lower() == 'true'
}