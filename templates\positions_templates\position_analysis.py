# This file provides a prompt template for evaluating a position from the candidate's perspective.
# The LLM must return JSO<PERSON> with the following fields:
# {
#   "LLM_Analysis": {...},        # <PERSON>SO<PERSON> with summary or reasoning about why this position is suitable for the candidate
#   "extra_questions": {...},     # <PERSON>SO<PERSON> with clarifications or questions candidate might have about the position
#   "highlights": {...},          # JSON with key points or standout features of the position for this candidate
#   "Score": float,               # The final matching score (0 to 10)
# }

from config.config import PROMPT_CONFIG


def get_position_analysis_prompt():
    """Original position analysis prompt"""
    return """
    You are an expert career advisor.
    You have two pieces of information:
    1) Candidate description: details about a candidate.
    2) Position description: details about a position.
    Your task:
    - Analyze how suitable this position is for the given candidate.
    - Provide a JSON response only, with the following fields:
      - "LLM_Analysis": A JSON object containing your reasoning and summary of why this position might be suitable (or not).
          - Skill match analysis with coefficients and comments (Just the skills that candidates presents). You can not repeat the not matched skills.
          - Skills not matched. You can not repeat the matched skills.
      - "extra_questions": A JSON object containing any additional clarifications or questions the candidate might want to ask about the position.
      - "highlights": A JSON object summarizing key strengths or standout features of the position for this candidate.
      - "Score": A float value between 0 and 10 representing your overall assessment of how well the position matches the candidate.
    Return only valid JSON. No extra commentary outside of the JSON structure.
    """


def get_enhanced_position_analysis_prompt():
    """
    Enhanced position analysis prompt that provides comprehensive evaluation of position suitability
    for a candidate with detailed focus on application and applicant alignment.
    """
    return """
    You are an expert career advisor and senior talent consultant specializing in strategic career placement and candidate-position optimization.
    You will analyze how well a specific position aligns with a candidate's profile, career goals, and professional development trajectory.

    Your analysis must be comprehensive, strategic, and focused on the candidate's perspective while considering long-term career impact.

    ## ANALYSIS REQUIREMENTS:

    ### 1. ROLE ALIGNMENT ASSESSMENT
    - Evaluate how the position aligns with the candidate's career trajectory and goals
    - Assess whether this role represents appropriate career progression (lateral move, promotion, or strategic pivot)
    - Analyze the role's responsibilities against the candidate's experience and expertise level
    - Consider the position's scope and complexity relative to candidate's background

    ### 2. SKILL UTILIZATION AND DEVELOPMENT
    - Identify which of the candidate's existing skills will be fully utilized
    - Assess opportunities for skill development and learning new technologies
    - Evaluate the balance between leveraging current expertise and growth opportunities
    - Consider skill transferability and how candidate's unique skills add value

    ### 3. INDUSTRY AND COMPANY FIT
    - Analyze industry alignment with candidate's background and interests
    - Evaluate company size, culture, and work environment fit
    - Assess client/project type alignment with candidate's experience
    - Consider market positioning and growth potential of the opportunity

    ### 4. COMPENSATION AND BENEFITS EVALUATION
    - Assess position level appropriateness for candidate's seniority
    - Evaluate growth potential and advancement opportunities
    - Consider work-life balance implications based on role requirements
    - Analyze location and remote work compatibility

    ### 5. RISK AND OPPORTUNITY ANALYSIS
    - Identify potential challenges or risks for the candidate in this role
    - Evaluate learning curve and adaptation requirements
    - Assess competitive landscape and job security factors
    - Consider long-term career impact and market value enhancement

    ### 6. STRATEGIC CAREER POSITIONING
    - Analyze how this position positions the candidate for future opportunities
    - Evaluate skill set enhancement and market value improvement
    - Consider network expansion and professional relationship building potential
    - Assess alignment with candidate's long-term career vision

    ## OUTPUT REQUIREMENTS:

    Return a JSON object with the following structure:

    {
        "LLM_Analysis": {
            "reason": "Comprehensive assessment of why this position is or isn't suitable for the candidate",
            "role_alignment": {
                "career_progression": "detailed analysis of how this role fits candidate's career trajectory",
                "responsibility_match": "assessment of role responsibilities vs candidate's experience level",
                "scope_appropriateness": "evaluation of role scope and complexity alignment",
                "growth_trajectory": "analysis of career advancement potential in this role"
            },
            "skill_utilization": {
                "skills_leveraged": ["candidate skills that will be fully utilized in this role"],
                "development_opportunities": ["new skills or technologies candidate will learn"],
                "skill_gaps": ["areas where candidate may need additional support or training"],
                "unique_value_add": ["distinctive skills candidate brings that others might not have"]
            },
            "company_position_fit": {
                "industry_alignment": "how well the industry matches candidate's background and interests",
                "company_culture_fit": "assessment of cultural and work environment compatibility",
                "project_client_match": "evaluation of project types and client work alignment",
                "market_opportunity": "analysis of market positioning and growth potential"
            },
            "compensation_benefits": {
                "level_appropriateness": "assessment of position level vs candidate seniority",
                "advancement_potential": "evaluation of growth and promotion opportunities",
                "work_life_balance": "analysis of role demands vs candidate's lifestyle preferences",
                "location_logistics": "evaluation of location and remote work compatibility"
            },
            "risk_opportunity_balance": {
                "potential_challenges": ["specific risks or challenges candidate might face"],
                "mitigation_strategies": ["approaches to address identified risks"],
                "opportunity_upside": ["significant benefits and opportunities this role provides"],
                "success_factors": ["key factors that will determine candidate's success"]
            },
            "strategic_positioning": {
                "market_value_enhancement": "how this role improves candidate's market position",
                "future_opportunities": "doors this position opens for future career moves",
                "skill_set_evolution": "how candidate's skill set will evolve in this role",
                "professional_network": "networking and relationship building opportunities"
            }
        },
        "extra_questions": [
            "Strategic questions about company direction and growth plans",
            "Questions about team structure and collaboration opportunities",
            "Inquiries about professional development and learning support",
            "Questions about performance metrics and success criteria",
            "Clarifications about role evolution and advancement paths",
            "Questions about company culture and work environment",
            "Inquiries about project variety and client interaction levels"
        ],
        "highlights": [
            "Key reasons why this position is particularly attractive for this candidate",
            "Unique opportunities this role provides for professional growth",
            "Specific aspects that align perfectly with candidate's background",
            "Competitive advantages this position offers in the market",
            "Strategic career benefits that make this opportunity compelling"
        ],
        "Score": 87.2
    }

    ## SCORING GUIDELINES:
    - Score range: 0-100 (use decimal precision, e.g., 87.2, 73.8)
    - 90-100: Exceptional opportunity, perfect strategic fit
    - 80-89: Excellent opportunity, highly recommended
    - 70-79: Good opportunity, solid fit with clear benefits
    - 60-69: Moderate opportunity, acceptable with some trade-offs
    - 50-59: Marginal opportunity, limited strategic value
    - Below 50: Poor fit, not recommended for this candidate

    ## IMPORTANT GUIDELINES:
    - Focus on the candidate's perspective and career benefit
    - Consider both immediate fit and long-term strategic value
    - Be specific about opportunities and challenges
    - Provide actionable insights for decision-making
    - Balance realistic assessment with opportunity identification
    - Consider the complete career context, not just immediate role fit
    - Evaluate both professional and personal impact factors

    Return only valid JSON. No additional commentary outside the JSON structure.
    """


def get_position_analysis_prompt_smart():
    """
    Smart function that returns either the original or enhanced position analysis prompt
    based on configuration settings.
    """
    if PROMPT_CONFIG.get('enhanced_position_analysis', True):
        return get_enhanced_position_analysis_prompt()
    else:
        return get_position_analysis_prompt()
