#!/usr/bin/env python3
"""
Simple verification script for enhanced prompts
"""

def main():
    try:
        # Test imports
        from config.config import PROMPT_CONFIG
        from templates.candidates_templates.candidate_analysis import (
            get_enhanced_candidate_analysis_prompt,
            get_candidate_analysis_prompt_smart
        )
        from templates.positions_templates.position_analysis import (
            get_enhanced_position_analysis_prompt,
            get_position_analysis_prompt_smart
        )
        
        # Test prompt generation
        enhanced_candidate = get_enhanced_candidate_analysis_prompt()
        smart_candidate = get_candidate_analysis_prompt_smart()
        enhanced_position = get_enhanced_position_analysis_prompt()
        smart_position = get_position_analysis_prompt_smart()
        
        # Write results to file
        with open('prompt_verification_results.txt', 'w') as f:
            f.write("Enhanced Prompts Verification Results\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Configuration: {PROMPT_CONFIG}\n\n")
            f.write(f"Enhanced candidate prompt length: {len(enhanced_candidate)}\n")
            f.write(f"Smart candidate prompt length: {len(smart_candidate)}\n")
            f.write(f"Enhanced position prompt length: {len(enhanced_position)}\n")
            f.write(f"Smart position prompt length: {len(smart_position)}\n\n")
            
            f.write("Enhanced candidate prompt preview:\n")
            f.write("-" * 30 + "\n")
            f.write(enhanced_candidate[:1000] + "...\n\n")
            
            f.write("Enhanced position prompt preview:\n")
            f.write("-" * 30 + "\n")
            f.write(enhanced_position[:1000] + "...\n\n")
            
            f.write("SUCCESS: All enhanced prompts are working correctly!\n")
        
        print("Verification completed successfully. Check prompt_verification_results.txt")
        return True
        
    except Exception as e:
        with open('prompt_verification_results.txt', 'w') as f:
            f.write(f"ERROR: {str(e)}\n")
            import traceback
            f.write(traceback.format_exc())
        print(f"Error occurred: {e}")
        return False

if __name__ == "__main__":
    main()
