{"roles": ["Software Engineer", "Backend Developer", "Full Stack Developer"], "response": {"roles": null, "skills": [{"name": "Python", "proficiency_level": "Advanced", "years_of_experience": 5}, {"name": "JavaScript", "proficiency_level": "Intermediate", "years_of_experience": 3}, {"name": "Java", "proficiency_level": "Intermediate", "years_of_experience": 3}, {"name": "Git", "proficiency_level": "Advanced", "years_of_experience": 5}, {"name": "Web Development", "proficiency_level": "Intermediate", "years_of_experience": 3}, {"name": "Database Management", "proficiency_level": "Intermediate", "years_of_experience": 3}], "summary": "Highly skilled Software Engineer with 3+ years of experience in developing high-quality software solutions. Proficient in modern programming languages such as Python, JavaScript, and Java. Strong problem-solving and analytical skills with excellent communication and teamwork abilities.", "projects": null, "education": [{"degree": "Bachelor of Science", "end_date": "31 May 2018", "location": "New York, USA", "start_date": "01 Sep 2014", "description": "", "field_of_study": "Computer Science", "institution_name": "Stanford University"}], "languages": [{"language": "English", "proficiency_level": "C1"}], "references": null, "soft_skills": [{"name": "Problem-Solving", "description": "Strong analytical and problem-solving skills"}, {"name": "Teamwork", "description": "Excellent communication and teamwork abilities"}, {"name": "Leadership", "description": "Ability to mentor junior developers and share knowledge with the team"}], "personal_info": {"city": "San Francisco", "email": "<EMAIL>", "address": "", "country": "USA", "website": "", "full_name": "Ideal Candidate", "phone_number": "", "linkedin_profile": "linkedin.com/in/idealcandidate"}, "certifications": null, "work_experience": [{"skills": [{"name": "Python", "proficiency_level": "Advanced", "years_of_experience": 5}, {"name": "JavaScript", "proficiency_level": "Intermediate", "years_of_experience": 3}, {"name": "Git", "proficiency_level": "Advanced", "years_of_experience": 5}], "end_date": "Currently working", "location": "San Francisco, USA", "job_title": "Software Engineer", "start_date": "01 Jan 2020", "company_name": "Tech Company", "responsibilities": ["Design, develop, and maintain software applications", "Collaborate with product managers and designers to implement features", "Write clean, efficient, and well-documented code"]}]}}