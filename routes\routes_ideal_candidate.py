"""
API routes for ideal candidate functionality.
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from datetime import datetime

from models.ideal_candidate import (
    IdealCandidateFilters,
    IdealCandidate,
    CandidateMatchRequest,
    CandidateMatchResponse,
    PositionMatchRequest
)
from services.stateless_ideal_candidate_service import stateless_ideal_candidate_service
from controllers.ideal_candidate_controller import (
    get_ideal_candidate_by_id,
    get_ideal_candidate_by_position_id,
    get_ideal_candidates_by_filters,
    delete_ideal_candidate
)
from services.ideal_candidate_embedding_service import ideal_candidate_embedding_service
from utils.ideal_candidate_query_utils import (
    check_ideal_candidate_availability,
    get_positions_with_ideal_candidates,
    get_matching_statistics
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ideal-candidates", tags=["ideal-candidates"])



@router.post("/generate/{position_id}")
def generate_ideal_candidate(
    position_id: str
):
    """
    Generate an ideal candidate for a position (stateless - always generates fresh).

    Args:
        position_id: The position ID to generate ideal candidate for

    Returns:
        Ideal candidate profile in candidate JSON format
    """
    try:
        logger.info(f"Generating stateless ideal candidate for position {position_id}")

        # Use the stateless service that doesn't require database storage
        response = stateless_ideal_candidate_service.generate_ideal_candidate_for_position(
            position_id=position_id,
            position_info=None  # Will use fallback if position not found
        )

        if response.generation_success:
            logger.info(f"Successfully generated ideal candidate for position {position_id}")
            # Return the ideal candidate info directly in candidate format
            return response.ideal_candidate_info
        else:
            logger.warning(f"Generated fallback ideal candidate: {response.error_message}")
            # Still return the fallback profile
            return response.ideal_candidate_info

    except Exception as e:
        logger.error(f"Error in generate ideal candidate endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{ideal_candidate_id}", response_model=IdealCandidate)
def get_ideal_candidate(ideal_candidate_id: str):
    """
    Get an ideal candidate by ID.
    
    Args:
        ideal_candidate_id: The ID of the ideal candidate to retrieve
        
    Returns:
        The ideal candidate object
    """
    try:
        ideal_candidate = get_ideal_candidate_by_id(ideal_candidate_id)
        
        if not ideal_candidate:
            raise HTTPException(status_code=404, detail=f"Ideal candidate {ideal_candidate_id} not found")
        
        return ideal_candidate
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting ideal candidate {ideal_candidate_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/position/{position_id}", response_model=IdealCandidate)
def get_ideal_candidate_for_position(position_id: str):
    """
    Get the ideal candidate for a specific position.
    
    Args:
        position_id: The position ID to get ideal candidate for
        
    Returns:
        The ideal candidate object
    """
    try:
        ideal_candidate = get_ideal_candidate_by_position_id(position_id)
        
        if not ideal_candidate:
            raise HTTPException(status_code=404, detail=f"No ideal candidate found for position {position_id}")
        
        return ideal_candidate
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting ideal candidate for position {position_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{ideal_candidate_id}")
def delete_ideal_candidate_endpoint(ideal_candidate_id: str):
    """
    Delete an ideal candidate by ID (soft delete).
    
    Args:
        ideal_candidate_id: The ID of the ideal candidate to delete
        
    Returns:
        Success message
    """
    try:
        success = delete_ideal_candidate(ideal_candidate_id, deleted_by="api_user")
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Ideal candidate {ideal_candidate_id} not found")
        
        return {"message": f"Ideal candidate {ideal_candidate_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting ideal candidate {ideal_candidate_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search")
def search_ideal_candidates(filters: IdealCandidateFilters, limit: int = 50, offset: int = 0):
    """
    Search for ideal candidates based on filters.
    
    Args:
        filters: The filters to apply
        limit: Maximum number of results to return
        offset: Number of results to skip
        
    Returns:
        List of ideal candidates matching the filters
    """
    try:
        ideal_candidates = get_ideal_candidates_by_filters(filters, limit, offset)
        
        return {
            "ideal_candidates": ideal_candidates,
            "count": len(ideal_candidates),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error searching ideal candidates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/position/{position_id}/availability")
def check_ideal_candidate_availability_endpoint(position_id: str):
    """
    Check if an ideal candidate exists and is ready for matching for a given position.
    
    Args:
        position_id: The position ID to check
        
    Returns:
        Availability information for the ideal candidate
    """
    try:
        availability = check_ideal_candidate_availability(position_id)
        return availability
        
    except Exception as e:
        logger.error(f"Error checking ideal candidate availability for position {position_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/embeddings/{ideal_candidate_id}/update")
def update_ideal_candidate_embedding(ideal_candidate_id: str):
    """
    Update the embedding for an ideal candidate.
    
    Args:
        ideal_candidate_id: The ID of the ideal candidate to update embedding for
        
    Returns:
        Success status
    """
    try:
        success = ideal_candidate_embedding_service.update_ideal_candidate_embedding(ideal_candidate_id)
        
        if success:
            return {"message": f"Embedding updated successfully for ideal candidate {ideal_candidate_id}"}
        else:
            raise HTTPException(status_code=400, detail=f"Failed to update embedding for ideal candidate {ideal_candidate_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating embedding for ideal candidate {ideal_candidate_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/embeddings/statistics")
def get_embedding_statistics(proj_id: Optional[str] = Query(None, description="Project ID to filter by")):
    """
    Get statistics about ideal candidate embeddings.
    
    Args:
        proj_id: Optional project ID to filter by
        
    Returns:
        Embedding statistics
    """
    try:
        statistics = ideal_candidate_embedding_service.get_embedding_statistics(proj_id)
        return statistics
        
    except Exception as e:
        logger.error(f"Error getting embedding statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/positions/with-ideal-candidates")
def get_positions_with_ideal_candidates_endpoint(
    proj_id: Optional[str] = Query(None, description="Project ID to filter by"),
    limit: int = Query(100, description="Maximum number of positions to return")
):
    """
    Get all positions that have ideal candidates with embeddings.
    
    Args:
        proj_id: Optional project ID to filter by
        limit: Maximum number of positions to return
        
    Returns:
        List of positions with their ideal candidate information
    """
    try:
        positions = get_positions_with_ideal_candidates(proj_id, limit)
        
        return {
            "positions": positions,
            "count": len(positions),
            "proj_id": proj_id,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Error getting positions with ideal candidates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/matching/statistics")
def get_matching_statistics_endpoint(proj_id: Optional[str] = Query(None, description="Project ID to filter by")):
    """
    Get statistics about ideal candidate matching readiness.
    
    Args:
        proj_id: Optional project ID to filter by
        
    Returns:
        Matching statistics
    """
    try:
        statistics = get_matching_statistics(proj_id)
        return statistics
        
    except Exception as e:
        logger.error(f"Error getting matching statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/similar/{ideal_candidate_id}")
def find_similar_ideal_candidates(
    ideal_candidate_id: str,
    limit: int = Query(5, description="Maximum number of similar candidates to return"),
    proj_id: Optional[str] = Query(None, description="Project ID to filter by")
):
    """
    Find ideal candidates similar to a reference ideal candidate.
    
    Args:
        ideal_candidate_id: The ID of the reference ideal candidate
        limit: Maximum number of similar candidates to return
        proj_id: Optional project ID to filter by
        
    Returns:
        List of similar ideal candidates with similarity scores
    """
    try:
        similar_candidates = ideal_candidate_embedding_service.find_similar_ideal_candidates(
            reference_ideal_candidate_id=ideal_candidate_id,
            limit=limit,
            proj_id=proj_id
        )
        
        return {
            "similar_ideal_candidates": similar_candidates,
            "reference_ideal_candidate_id": ideal_candidate_id,
            "count": len(similar_candidates),
            "limit": limit,
            "proj_id": proj_id
        }
        
    except Exception as e:
        logger.error(f"Error finding similar ideal candidates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/match")
def execute_ideal_candidate_match(request: PositionMatchRequest):
    """
    Match a position against all candidates in the database using ideal candidate-based approach.

    Args:
        request: The matching request containing only position_id

    Returns:
        Matching results with all candidates ranked by similarity to the position's ideal candidate
    """
    try:
        from services.ideal_candidate_matching_service import ideal_candidate_matching_service

        logger.info(f"Executing ideal candidate-based matching for position {request.position_id}")

        # Match all candidates against the position using ideal candidate approach
        # Using high limit to get all candidates, hasFeedback=2 (both), batch_mode=True
        result = ideal_candidate_matching_service.match_candidates_with_ideal_candidate(
            position_id=request.position_id,
            limit=1000,  # High limit to match against ALL candidates
            hasFeedback=2,  # Include both candidates with and without feedback
            batch_mode=True  # Use batch processing for efficiency
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in ideal candidate-based matching: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/candidate-match", response_model=CandidateMatchResponse)
def execute_candidate_match(request: CandidateMatchRequest):
    """
    Match all candidates against a specific ideal candidate.

    Args:
        request: The matching request containing ideal_candidate_id and options

    Returns:
        Response containing matched candidates ranked by similarity
    """
    try:
        logger.info(f"Executing candidate matching for ideal candidate {request.ideal_candidate_id}")

        # Import the matching service
        from services.ideal_candidate_matching_service import ideal_candidate_matching_service

        # Execute the matching using the ideal candidate ID
        result = ideal_candidate_matching_service.match_candidates_by_ideal_candidate_id(
            ideal_candidate_id=request.ideal_candidate_id,
            limit=request.limit,
            hasFeedback=request.hasFeedback,
            batch_mode=request.batch_mode
        )

        return CandidateMatchResponse(
            ideal_candidate_id=request.ideal_candidate_id,
            total_candidates_found=len(result.get("candidates", [])),
            candidates=result.get("candidates", []),
            matching_success=True,
            error_message=None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in candidate matching: {e}")
        return CandidateMatchResponse(
            ideal_candidate_id=request.ideal_candidate_id,
            total_candidates_found=0,
            candidates=[],
            matching_success=False,
            error_message=str(e)
        )
