
version: "3.11"  # Use a recent version

services:
  smarthr:
    restart: always

    build:
      context: .  # Use the current directory as build context
      dockerfile: Dockerfile
    ports:
      - "8080:8080"  # Map port 8080 on the host to port 8080 in the container
    environment:
      - AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=${AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - OPENAI_API_VERSION=${OPENAI_API_VERSION}
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      - GROQ_API_KEY= ${GROQ_API_KEY}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - APPLICATIONINSIGHTS_CONNECTION_STRING=${APPLICATIONINSIGHTS_CONNECTION_STRING}
      - POSITION_MATCH_MODELS_ORDER=${POSITION_MATCH_MODELS_ORDER}
      - IDEAL_CANDIDATE_MODELS_ORDER=${IDEAL_CANDIDATE_MODELS_ORDER}

  db-init:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
    command: python scripts/init_db.py
    profiles:
      - init

