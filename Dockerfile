FROM python:3.11-slim

WORKDIR /app
# Install essential packages for the application
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .

# Create a non-root user
RUN useradd --create-home appuser \
    && chown -R appuser:appuser /app
USER appuser
# Expose the FastAPI port
EXPOSE 8080
# Set environment variables (you can also use a .env file and copy it)
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080", "--log-level", "info"]

