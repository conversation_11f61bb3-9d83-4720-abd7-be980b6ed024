#!/bin/bash

echo "Building and running database initialization through Docker..."

# Build the Docker image if it doesn't exist
echo "Building Docker image..."
docker-compose build

# Run the database initialization script through the container
echo "Running database initialization script..."
docker-compose run --rm smarthr python -c "
import sys
import os
sys.path.append('/app')

# Import the database initialization
try:
    from scripts.init_db import main
    print('Running database initialization...')
    main()
    print('✅ Database initialization completed successfully!')
except Exception as e:
    print(f'❌ Database initialization failed: {e}')
    sys.exit(1)
"

echo "Database initialization process completed."
